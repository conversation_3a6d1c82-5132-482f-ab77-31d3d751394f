// ===== FIREBASE AUTHENTICATION SYSTEM =====

// Firebase imports (loaded via CDN in HTML)
let auth;
let signInWithEmailAndPassword;
let onAuthStateChanged;
let signOut;

// Initialize Firebase Auth functions when available
function initializeFirebaseAuth() {
    if (window.firebaseAuth) {
        auth = window.firebaseAuth;
        
        // Import Firebase Auth functions
        import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js')
            .then((authModule) => {
                signInWithEmailAndPassword = authModule.signInWithEmailAndPassword;
                onAuthStateChanged = authModule.onAuthStateChanged;
                signOut = authModule.signOut;
                
                // Set up auth state listener
                setupAuthStateListener();
            })
            .catch((error) => {
                console.error('Error loading Firebase Auth:', error);
            });
    } else {
        // Retry after a short delay
        setTimeout(initializeFirebaseAuth, 100);
    }
}

// ===== AUTHENTICATION STATE MANAGEMENT =====

function setupAuthStateListener() {
    onAuthStateChanged(auth, (user) => {
        if (user) {
            // User is signed in
            handleAuthenticatedUser(user);
        } else {
            // User is signed out
            handleUnauthenticatedUser();
        }
    });
}

function handleAuthenticatedUser(user) {
    const currentPage = window.location.pathname;
    const isLoginPage = currentPage.includes('login.html') || currentPage.includes('admin-login.html');
    
    if (isLoginPage) {
        // Redirect based on user role
        redirectUserBasedOnRole(user.email);
    }
}

function handleUnauthenticatedUser() {
    const currentPage = window.location.pathname;
    const isProtectedPage = currentPage.includes('admin-panel.html') || 
                           currentPage.includes('index.html') || 
                           currentPage === '/' || 
                           currentPage === '';
    
    if (isProtectedPage) {
        // Redirect to appropriate login page
        if (currentPage.includes('admin-panel.html')) {
            window.location.href = 'admin-login.html';
        } else {
            window.location.href = 'login.html';
        }
    }
}

function redirectUserBasedOnRole(email) {
    // Check if user is admin based on email pattern
    const isAdmin = isAdminEmail(email);
    
    if (isAdmin) {
        window.location.href = 'admin-panel.html';
    } else {
        window.location.href = 'index.html';
    }
}

function isAdminEmail(email) {
    // Define admin email patterns
    const adminPatterns = [
        '@bit.com',
        '@admin.bit.com',
        'admin@',
        '.admin@'
    ];
    
    return adminPatterns.some(pattern => email.toLowerCase().includes(pattern.toLowerCase()));
}

// ===== STUDENT LOGIN FUNCTIONALITY =====

function initializeStudentLogin() {
    const loginForm = document.getElementById('studentLoginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.getElementById('loginBtn');
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    const passwordToggle = document.getElementById('passwordToggle');
    
    // Initialize Firebase Auth
    initializeFirebaseAuth();
    
    // Password toggle functionality
    if (passwordToggle) {
        passwordToggle.addEventListener('click', () => {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const eyeIcon = passwordToggle.querySelector('.eye-icon');
            if (type === 'text') {
                eyeIcon.innerHTML = `
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                    <line x1="1" y1="1" x2="23" y2="23"></line>
                `;
            } else {
                eyeIcon.innerHTML = `
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                `;
            }
        });
    }
    
    // Form submission
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = emailInput.value.trim();
            const password = passwordInput.value;
            
            if (!email || !password) {
                showError('Please fill in all fields.');
                return;
            }
            
            await handleStudentLogin(email, password);
        });
    }
    
    function showError(message) {
        errorText.textContent = message;
        errorMessage.style.display = 'flex';
        setTimeout(() => {
            errorMessage.style.display = 'none';
        }, 5000);
    }
    
    function showLoading(show) {
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoader = loginBtn.querySelector('.btn-loader');
        
        if (show) {
            btnText.style.display = 'none';
            btnLoader.style.display = 'flex';
            loginBtn.disabled = true;
        } else {
            btnText.style.display = 'block';
            btnLoader.style.display = 'none';
            loginBtn.disabled = false;
        }
    }
    
    async function handleStudentLogin(email, password) {
        showLoading(true);
        
        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Check if user is trying to access student portal with admin credentials
            if (isAdminEmail(email)) {
                showError('Admin accounts should use the Admin Login page.');
                await signOut(auth);
                showLoading(false);
                return;
            }
            
            // Success - redirect will be handled by auth state listener
            console.log('Student login successful:', user.email);
            
        } catch (error) {
            showLoading(false);
            handleAuthError(error, showError);
        }
    }
}

// ===== ADMIN LOGIN FUNCTIONALITY =====

function initializeAdminLogin() {
    const loginForm = document.getElementById('adminLoginForm');
    const emailInput = document.getElementById('adminEmail');
    const passwordInput = document.getElementById('adminPassword');
    const loginBtn = document.getElementById('adminLoginBtn');
    const errorMessage = document.getElementById('adminErrorMessage');
    const errorText = document.getElementById('adminErrorText');
    const passwordToggle = document.getElementById('adminPasswordToggle');
    
    // Initialize Firebase Auth
    initializeFirebaseAuth();
    
    // Password toggle functionality
    if (passwordToggle) {
        passwordToggle.addEventListener('click', () => {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const eyeIcon = passwordToggle.querySelector('.eye-icon');
            if (type === 'text') {
                eyeIcon.innerHTML = `
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                    <line x1="1" y1="1" x2="23" y2="23"></line>
                `;
            } else {
                eyeIcon.innerHTML = `
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                `;
            }
        });
    }
    
    // Form submission
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = emailInput.value.trim();
            const password = passwordInput.value;
            
            if (!email || !password) {
                showError('Please fill in all fields.');
                return;
            }
            
            await handleAdminLogin(email, password);
        });
    }
    
    function showError(message) {
        errorText.textContent = message;
        errorMessage.style.display = 'flex';
        setTimeout(() => {
            errorMessage.style.display = 'none';
        }, 5000);
    }
    
    function showLoading(show) {
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoader = loginBtn.querySelector('.btn-loader');
        
        if (show) {
            btnText.style.display = 'none';
            btnLoader.style.display = 'flex';
            loginBtn.disabled = true;
        } else {
            btnText.style.display = 'block';
            btnLoader.style.display = 'none';
            loginBtn.disabled = false;
        }
    }
    
    async function handleAdminLogin(email, password) {
        showLoading(true);
        
        try {
            // Validate admin email pattern first
            if (!isAdminEmail(email)) {
                showError('Invalid admin email format. Please use an authorized admin account.');
                showLoading(false);
                return;
            }
            
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Success - redirect will be handled by auth state listener
            console.log('Admin login successful:', user.email);
            
        } catch (error) {
            showLoading(false);
            handleAuthError(error, showError);
        }
    }
}

// ===== ERROR HANDLING =====

function handleAuthError(error, showErrorCallback) {
    let errorMessage = 'An error occurred. Please try again.';
    
    switch (error.code) {
        case 'auth/user-not-found':
            errorMessage = 'No account found with this email address.';
            break;
        case 'auth/wrong-password':
            errorMessage = 'Incorrect password. Please try again.';
            break;
        case 'auth/invalid-email':
            errorMessage = 'Please enter a valid email address.';
            break;
        case 'auth/user-disabled':
            errorMessage = 'This account has been disabled. Contact administrator.';
            break;
        case 'auth/too-many-requests':
            errorMessage = 'Too many failed attempts. Please try again later.';
            break;
        case 'auth/network-request-failed':
            errorMessage = 'Network error. Please check your connection.';
            break;
        case 'auth/invalid-credential':
            errorMessage = 'Invalid credentials. Please check your email and password.';
            break;
        default:
            console.error('Auth error:', error);
            errorMessage = 'Login failed. Please try again.';
    }
    
    showErrorCallback(errorMessage);
}

// ===== LOGOUT FUNCTIONALITY =====

function initializeLogout() {
    const logoutBtn = document.getElementById('logoutBtn');
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', async () => {
            try {
                await signOut(auth);
                window.location.href = 'login.html';
            } catch (error) {
                console.error('Logout error:', error);
            }
        });
    }
}

// ===== PAGE PROTECTION =====

function protectPage() {
    initializeFirebaseAuth();
}

// ===== INITIALIZATION =====

// Auto-initialize based on current page
document.addEventListener('DOMContentLoaded', () => {
    const currentPage = window.location.pathname;
    
    if (currentPage.includes('login.html')) {
        // Student login page
        if (typeof initializeStudentLogin === 'function') {
            initializeStudentLogin();
        }
    } else if (currentPage.includes('admin-login.html')) {
        // Admin login page
        if (typeof initializeAdminLogin === 'function') {
            initializeAdminLogin();
        }
    } else {
        // Protected pages
        protectPage();
        initializeLogout();
    }
});

// Export functions for global access
window.initializeStudentLogin = initializeStudentLogin;
window.initializeAdminLogin = initializeAdminLogin;
window.protectPage = protectPage;
